<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shutdown Warning</title>
    <style>
        /* Shutdown Overlay CSS with iPhone-style slide to unlock */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            /* 预设背景色避免白屏 */
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }

        .overlay-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            /* 确保立即可见 */
            opacity: 1;
            visibility: visible;
        }

        /* Loading 状态样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
            transition: opacity 0.5s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fbbf24;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.1rem;
            opacity: 0.9;
            animation: pulse 2s infinite;
        }

        .background-blur {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
            backdrop-filter: blur(20px);
            z-index: -1;
        }

        .warning-content {
            text-align: center;
            color: white;
            max-width: 900px;
            width: 90%;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: slideInUp 0.6s ease-out;
            opacity: 0;
            transform: translateY(50px) scale(0.95);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .warning-content.ready {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .warning-icon {
            margin-bottom: 20px;
            color: #fbbf24;
            animation: pulse 2s infinite;
        }

        .warning-icon svg {
            filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.3));
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        h1 {
            font-size: 2.2rem;
            font-weight: 300;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .warning-message {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 40px;
            margin-bottom: 40px;
        }

        .countdown-section {
            flex: 0 0 auto;
            min-width: 200px;
        }

        .countdown-display {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sentence-section {
            flex: 1;
            text-align: left;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .sentence-content {
            font-family: 'LXGWWenKai-Regular', serif;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.95);
        }

        .sentence-meta {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            font-style: italic;
        }

        #countdown-timer {
            font-size: 4rem;
            font-weight: 200;
            color: #fbbf24;
            text-shadow: 0 4px 8px rgba(251, 191, 36, 0.3);
            display: block;
            line-height: 1;
        }

        .countdown-label {
            font-size: 1rem;
            opacity: 0.8;
            margin-top: 5px;
            display: block;
        }

        .slide-to-cancel-container {
            margin: 40px 0;
        }

        .slide-track {
            position: relative;
            width: 100%;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            cursor: pointer;
        }

        .slide-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
        }

        .slide-text {
            font-size: 1rem;
            font-weight: 500;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            transition: opacity 0.3s ease, color 0.3s ease;
            pointer-events: none;
        }

        .slide-arrow {
            position: absolute;
            right: 20px;
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.6);
            animation: slideHint 2s infinite;
            pointer-events: none;
        }

        @keyframes slideHint {
            0%, 100% {
                transform: translateX(0);
                opacity: 0.6;
            }
            50% {
                transform: translateX(10px);
                opacity: 1;
            }
        }

        .slide-button {
            position: absolute;
            left: 4px;
            top: 4px;
            width: 52px;
            height: 52px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 26px;
            cursor: grab;
            transition: box-shadow 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .slide-button:active {
            cursor: grabbing;
        }

        .slide-button-inner {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            border-radius: 26px;
            transition: all 0.3s ease;
        }

        .slide-track.dragging .slide-button {
            transition: none;
        }

        .slide-track.dragging .slide-arrow {
            opacity: 0;
        }

        .additional-info {
            margin-top: 30px;
        }

        .additional-info p {
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .small-text {
            font-size: 0.9rem;
            opacity: 0.6;
        }

        /* 字体加载 */
        @font-face {
            font-family: 'LXGWWenKai-Regular';
            src: url('LXGWWenKai-Regular.ttf') format('truetype');
            font-display: swap;
        }

        /* Responsive adjustments */
        @media (max-width: 800px) {
            .main-content {
                flex-direction: column;
                gap: 20px;
            }

            .countdown-section {
                min-width: auto;
                align-self: center;
            }

            .sentence-section {
                text-align: center;
            }
        }

        @media (max-width: 600px) {
            .warning-content {
                margin: 20px;
                padding: 30px 20px;
            }

            h1 {
                font-size: 1.8rem;
            }

            #countdown-timer {
                font-size: 3rem;
            }

            .slide-track {
                height: 50px;
            }

            .slide-button {
                width: 42px;
                height: 42px;
                border-radius: 21px;
            }

            .slide-button-inner {
                border-radius: 21px;
            }

            .sentence-content {
                font-size: 1rem;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .overlay-container {
                background: #000000;
            }

            .warning-content {
                background: rgba(255, 255, 255, 0.95);
                color: #000000;
                border: 2px solid #ffffff;
            }

            .slide-track {
                background: #333333;
                border: 2px solid #ffffff;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .warning-content {
                animation: none;
            }

            .warning-icon {
                animation: none;
            }

            .slide-arrow {
                animation: none;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.8; }
            }
        }
    </style>
</head>
<body>
    <!-- Loading 覆盖层 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Initializing Shutdown Warning...</div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="overlay-container">
        <div class="warning-content" id="warning-content">
            <div class="warning-icon">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 8V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 16H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>

            <h1>System Shutdown Scheduled</h1>
            <p class="warning-message">Your computer will shutdown automatically in:</p>

            <div class="main-content">
                <div class="countdown-section">
                    <div class="countdown-display">
                        <span id="countdown-timer">30</span>
                        <span class="countdown-label">seconds</span>
                    </div>
                </div>

                <div class="sentence-section">
                    <div class="sentence-content" id="sentence-content">
                        Loading wisdom...
                    </div>
                    <div class="sentence-meta" id="sentence-meta">

                    </div>
                </div>
            </div>

            <div class="slide-to-cancel-container">
                <div class="slide-track">
                    <div class="slide-background">
                        <span class="slide-text">Slide to Cancel</span>
                        <div class="slide-arrow">→</div>
                    </div>
                    <div class="slide-button" id="slide-button">
                        <div class="slide-button-inner">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="additional-info">
                <p>To prevent automatic shutdown, slide the button to the right</p>
                <p class="small-text">This shutdown was triggered by your scheduled settings</p>
            </div>
        </div>

        <div class="background-blur"></div>
    </div>

    <script src="shutdown_overlay.js"></script>
</body>
</html>
