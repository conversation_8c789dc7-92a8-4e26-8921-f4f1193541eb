"""
Shutdown Overlay Window for Auto Shutdown Application
Runs as a separate process to show the shutdown warning overlay
"""

import webview
import sys
import os
import threading
import time
import subprocess
import json
import random
from config_manager import ConfigManager


class ShutdownOverlayAPI:
    def __init__(self):
        self.config_manager = ConfigManager()
        config = self.config_manager.load_config()
        self.countdown_duration = config.get('settings', {}).get('countdown_duration', 30)
        self.countdown_thread = None
        self.is_active = True
        self.window_ready = False
        self.sentences_data = []

        # 加载句子数据
        self.load_sentences()

        # 不立即启动倒计时，等待窗口准备完成
        print(f"Shutdown overlay initialized with {self.countdown_duration}s countdown")
    
    def load_sentences(self):
        """Load sentences from JSON file"""
        try:
            base_path = os.path.dirname(os.path.abspath(__file__))
            sentences_path = os.path.join(base_path, 'static', 'sentences.json')

            if os.path.exists(sentences_path):
                with open(sentences_path, 'r', encoding='utf-8') as f:
                    self.sentences_data = json.load(f)
                print(f"Loaded {len(self.sentences_data)} sentence collections")
            else:
                print(f"Sentences file not found: {sentences_path}")
                self.sentences_data = []
        except Exception as e:
            print(f"Failed to load sentences: {e}")
            self.sentences_data = []

    def get_countdown_duration(self):
        """Get countdown duration for JavaScript"""
        return self.countdown_duration

    def get_random_sentence(self):
        """Get a random sentence for JavaScript"""
        try:
            # 如果没有加载到句子数据，使用内置的备用句子
            if not self.sentences_data:
                fallback_sentences = self.get_fallback_sentences()
                random_collection = random.choice(fallback_sentences)
                random_sentence = random.choice(random_collection['content'])
                return {
                    "content": random_sentence,
                    "title": random_collection['title'],
                    "author": random_collection['author']
                }

            # 随机选择一个文集
            random_collection = random.choice(self.sentences_data)

            # 随机选择该文集中的一句
            random_sentence = random.choice(random_collection['content'])

            return {
                "content": random_sentence,
                "title": random_collection['title'],
                "author": random_collection['author']
            }
        except Exception as e:
            print(f"Failed to get random sentence: {e}")
            # 即使出错也返回随机的备用句子
            fallback_sentences = self.get_fallback_sentences()
            random_collection = random.choice(fallback_sentences)
            random_sentence = random.choice(random_collection['content'])
            return {
                "content": random_sentence,
                "title": random_collection['title'],
                "author": random_collection['author']
            }

    def get_fallback_sentences(self):
        """Get fallback sentences when main data fails to load"""
        return [
            {
                "title": "论语",
                "author": "孔子",
                "content": [
                    "学而时习之，不亦说乎？",
                    "知之者不如好之者，好之者不如乐之者。",
                    "三人行，必有我师焉。",
                    "君子坦荡荡，小人长戚戚。",
                    "己所不欲，勿施于人。"
                ]
            },
            {
                "title": "道德经",
                "author": "老子",
                "content": [
                    "道可道，非常道。",
                    "知者不言，言者不知。",
                    "千里之行，始于足下。",
                    "上善若水，水善利万物而不争。",
                    "大道至简，知易行难。"
                ]
            },
            {
                "title": "古诗名句",
                "author": "诸子百家",
                "content": [
                    "山重水复疑无路，柳暗花明又一村。",
                    "海内存知己，天涯若比邻。",
                    "会当凌绝顶，一览众山小。",
                    "落红不是无情物，化作春泥更护花。",
                    "长风破浪会有时，直挂云帆济沧海。"
                ]
            }
        ]

    def window_loaded(self):
        """Called when the window is fully loaded and ready"""
        print("Window loaded, starting countdown...")
        self.window_ready = True
        self.start_countdown()
        return {"success": True}
    
    def cancel_shutdown(self):
        """Cancel shutdown (called from JavaScript)"""
        print("Shutdown cancelled by user")
        self.is_active = False
        # Close the window
        try:
            webview.windows[0].destroy()
        except:
            pass
        return {"success": True}
    
    def start_countdown(self):
        """Start the countdown timer"""
        def countdown():
            for remaining in range(self.countdown_duration, 0, -1):
                if not self.is_active:
                    return
                
                # Update countdown display
                try:
                    webview.windows[0].evaluate_js(f'updateCountdown({remaining})')
                except:
                    pass
                
                time.sleep(1)
            
            # Time's up - execute shutdown
            if self.is_active:
                self.execute_shutdown()
        
        self.countdown_thread = threading.Thread(target=countdown, daemon=True)
        self.countdown_thread.start()
    
    def execute_shutdown(self):
        """Execute system shutdown"""
        print("Executing shutdown...")
        try:
            if sys.platform == "win32":
                subprocess.run(['shutdown', '/s', '/f', '/t', '0'], check=True)
            elif sys.platform in ["linux", "darwin"]:
                subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            else:
                print(f"Unsupported platform: {sys.platform}")
        except subprocess.CalledProcessError as e:
            print(f"Failed to shutdown: {e}")
        except FileNotFoundError:
            print("Shutdown command not found - running in test mode")


def main():
    """Run the shutdown overlay window"""
    try:
        # Create API instance
        api = ShutdownOverlayAPI()

        # Get the correct path to the HTML file
        base_path = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(base_path, 'static', 'shutdown_overlay.html')

        # Verify the HTML file exists
        if not os.path.exists(html_path):
            print(f"HTML file not found: {html_path}")
            sys.exit(1)

        print(f"Loading shutdown overlay from: {html_path}")

        # Create fullscreen overlay window with optimized settings
        window = webview.create_window(
            'Shutdown Warning',
            html_path,
            fullscreen=True,
            on_top=True,
            shadow=False,
            resizable=False,
            js_api=api,
            minimized=False,
            focus=True,
            background_color='#1a1a2e'
        )

        # Start webview with optimized settings
        webview.start(
            debug=True,  # 启用调试模式查看JavaScript日志
            http_server=False,
            private_mode=False
        )

    except Exception as e:
        print(f"Shutdown overlay error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
